# 气体-厚度关系分析与预测系统

## 项目概述

本项目旨在分析气体浓度与材料厚度之间的关系，并通过卷积自编码器（CAE）模型实现双向预测。系统包含数据预处理、插值分析、相关性分析和深度学习模型训练与预测等功能模块。

## 目录结构

```
├── data/                  # 数据文件夹
│   ├── gas.xlsx           # 气体浓度数据
│   ├── thk.xlsx           # 厚度数据
│   └── No432_DataFiltered_Nano_20250422.CSV  # 原始数据
├── code_pre/              # 预处理代码
│   ├── THK_Interpolation_cubic.ipynb  # 三次样条插值
│   ├── THK_Interpolation_linear.ipynb # 线性插值
│   └── Correlation_analysis.ipynb     # 相关性分析
├── code_CAE/              # 深度学习模型代码
│   ├── model.py           # 模型定义
│   ├── dataset.py         # 数据集处理
│   ├── train.py           # 训练脚本
│   ├── predict.py         # 预测脚本
│   └── loss_plot.py       # 损失可视化
├── output_pre/            # 预处理输出结果
├── output_CAE/            # 模型输出结果
│   ├── checkpoints/       # 模型检查点
│   ├── img/               # 可视化图像
│   └── loss/              # 损失记录
```

## 功能模块

### 1. 数据预处理

- **插值处理**：提供线性插值和三次样条插值两种方法，将离散的厚度测量点插值到规则网格
- **数据标准化**：对输入数据进行标准化处理，提高模型训练效果

### 2. 相关性分析

- 计算气体浓度与厚度之间的皮尔森相关系数
- 进行线性回归分析，获取关系方程和决定系数
- 可视化气体-厚度关系

### 3. 深度学习模型

- **模型架构**：基于卷积自编码器（CAE）的双目标模型
- **训练目标**：同时优化气体重构和厚度预测
- **特点**：支持不同尺寸的图像块（patch）输入

### 4. 预测与评估

- 支持气体→厚度和厚度→气体的双向预测
- 提供预测结果的可视化比较
- 计算预测误差（MSE）评估模型性能

## 使用方法

1. **数据准备**：将气体浓度和厚度数据放入`data`文件夹
2. **数据预处理**：运行`code_pre`文件夹中的插值脚本
3. **模型训练**：
   ```
   cd code_CAE
   python train.py
   ```
4. **预测**：
   ```
   cd code_CAE
   python predict.py
   ```
5. **结果分析**：查看`output_CAE/img`文件夹中的可视化结果

## 环境要求

- Python 3.8+
- PyTorch 1.8+
- NumPy
- Pandas
- Matplotlib
- scikit-learn
- SciPy

## 注意事项

- 模型训练需要GPU加速以获得更好的性能
- 可通过调整`patch_size`参数优化模型性能