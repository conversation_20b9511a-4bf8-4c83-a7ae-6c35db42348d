{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["gas_file = \"gas.xlsx\"\n", "thk_file = \"linear_Interpolated_Data_With_Linear_Extrapolation.csv\"\n", "gas_df = pd.read_excel(gas_file, index_col=0)\n", "thk_df = pd.read_csv(thk_file)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>-1300</th>\n", "      <th>-1200</th>\n", "      <th>-1100</th>\n", "      <th>-1000</th>\n", "      <th>-900</th>\n", "      <th>-800</th>\n", "      <th>-700</th>\n", "      <th>-600</th>\n", "      <th>-500</th>\n", "      <th>-400</th>\n", "      <th>...</th>\n", "      <th>400</th>\n", "      <th>500</th>\n", "      <th>600</th>\n", "      <th>700</th>\n", "      <th>800</th>\n", "      <th>900</th>\n", "      <th>1000</th>\n", "      <th>1100</th>\n", "      <th>1200</th>\n", "      <th>1300</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1150</th>\n", "      <td>96.8</td>\n", "      <td>89.9</td>\n", "      <td>94.4</td>\n", "      <td>98.7</td>\n", "      <td>87.7</td>\n", "      <td>89.8</td>\n", "      <td>91.5</td>\n", "      <td>97.8</td>\n", "      <td>89.8</td>\n", "      <td>85.2</td>\n", "      <td>...</td>\n", "      <td>84.3</td>\n", "      <td>97.40</td>\n", "      <td>94.7</td>\n", "      <td>98.3</td>\n", "      <td>78.6</td>\n", "      <td>78.5</td>\n", "      <td>92.7</td>\n", "      <td>85.3</td>\n", "      <td>81.9</td>\n", "      <td>72.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1050</th>\n", "      <td>95.3</td>\n", "      <td>74.5</td>\n", "      <td>74.6</td>\n", "      <td>77.7</td>\n", "      <td>72.2</td>\n", "      <td>82.3</td>\n", "      <td>88.2</td>\n", "      <td>89.8</td>\n", "      <td>84.2</td>\n", "      <td>95.1</td>\n", "      <td>...</td>\n", "      <td>74.6</td>\n", "      <td>78.10</td>\n", "      <td>94.7</td>\n", "      <td>97.3</td>\n", "      <td>92.5</td>\n", "      <td>93.4</td>\n", "      <td>70.1</td>\n", "      <td>85.4</td>\n", "      <td>94.9</td>\n", "      <td>82.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>950</th>\n", "      <td>74.0</td>\n", "      <td>89.9</td>\n", "      <td>92.9</td>\n", "      <td>79.5</td>\n", "      <td>71.9</td>\n", "      <td>90.2</td>\n", "      <td>75.2</td>\n", "      <td>82.3</td>\n", "      <td>76.8</td>\n", "      <td>92.8</td>\n", "      <td>...</td>\n", "      <td>70.1</td>\n", "      <td>77.22</td>\n", "      <td>88.5</td>\n", "      <td>88.1</td>\n", "      <td>71.2</td>\n", "      <td>96.1</td>\n", "      <td>89.8</td>\n", "      <td>66.1</td>\n", "      <td>95.2</td>\n", "      <td>69.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>850</th>\n", "      <td>93.5</td>\n", "      <td>89.8</td>\n", "      <td>91.9</td>\n", "      <td>93.7</td>\n", "      <td>98.2</td>\n", "      <td>73.4</td>\n", "      <td>89.8</td>\n", "      <td>87.7</td>\n", "      <td>84.8</td>\n", "      <td>83.6</td>\n", "      <td>...</td>\n", "      <td>67.4</td>\n", "      <td>96.40</td>\n", "      <td>77.9</td>\n", "      <td>80.4</td>\n", "      <td>77.6</td>\n", "      <td>87.5</td>\n", "      <td>79.8</td>\n", "      <td>77.2</td>\n", "      <td>95.7</td>\n", "      <td>102.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>750</th>\n", "      <td>76.0</td>\n", "      <td>75.5</td>\n", "      <td>73.8</td>\n", "      <td>73.5</td>\n", "      <td>76.9</td>\n", "      <td>93.4</td>\n", "      <td>76.8</td>\n", "      <td>91.8</td>\n", "      <td>79.5</td>\n", "      <td>95.7</td>\n", "      <td>...</td>\n", "      <td>77.1</td>\n", "      <td>78.20</td>\n", "      <td>74.5</td>\n", "      <td>67.6</td>\n", "      <td>72.6</td>\n", "      <td>68.2</td>\n", "      <td>93.1</td>\n", "      <td>89.3</td>\n", "      <td>85.8</td>\n", "      <td>99.1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 27 columns</p>\n", "</div>"], "text/plain": ["      -1300  -1200  -1100  -1000  -900   -800   -700   -600   -500   -400   \\\n", "1150   96.8   89.9   94.4   98.7   87.7   89.8   91.5   97.8   89.8   85.2   \n", "1050   95.3   74.5   74.6   77.7   72.2   82.3   88.2   89.8   84.2   95.1   \n", "950    74.0   89.9   92.9   79.5   71.9   90.2   75.2   82.3   76.8   92.8   \n", "850    93.5   89.8   91.9   93.7   98.2   73.4   89.8   87.7   84.8   83.6   \n", "750    76.0   75.5   73.8   73.5   76.9   93.4   76.8   91.8   79.5   95.7   \n", "\n", "      ...   400    500    600    700    800    900    1000   1100   1200  \\\n", "1150  ...   84.3  97.40   94.7   98.3   78.6   78.5   92.7   85.3   81.9   \n", "1050  ...   74.6  78.10   94.7   97.3   92.5   93.4   70.1   85.4   94.9   \n", "950   ...   70.1  77.22   88.5   88.1   71.2   96.1   89.8   66.1   95.2   \n", "850   ...   67.4  96.40   77.9   80.4   77.6   87.5   79.8   77.2   95.7   \n", "750   ...   77.1  78.20   74.5   67.6   72.6   68.2   93.1   89.3   85.8   \n", "\n", "       1300  \n", "1150   72.6  \n", "1050   82.3  \n", "950    69.8  \n", "850   102.8  \n", "750    99.1  \n", "\n", "[5 rows x 27 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["gas_df.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["thk_matrix = np.zeros(gas_df.shape)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>X</th>\n", "      <th>Y</th>\n", "      <th>Value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1300</td>\n", "      <td>1150</td>\n", "      <td>663.888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-1200</td>\n", "      <td>1150</td>\n", "      <td>671.009</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-1100</td>\n", "      <td>1150</td>\n", "      <td>671.009</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-1000</td>\n", "      <td>1150</td>\n", "      <td>653.751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-900</td>\n", "      <td>1150</td>\n", "      <td>653.751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>643</th>\n", "      <td>900</td>\n", "      <td>-1150</td>\n", "      <td>668.462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>644</th>\n", "      <td>1000</td>\n", "      <td>-1150</td>\n", "      <td>668.462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>645</th>\n", "      <td>1100</td>\n", "      <td>-1150</td>\n", "      <td>663.832</td>\n", "    </tr>\n", "    <tr>\n", "      <th>646</th>\n", "      <td>1200</td>\n", "      <td>-1150</td>\n", "      <td>663.832</td>\n", "    </tr>\n", "    <tr>\n", "      <th>647</th>\n", "      <td>1300</td>\n", "      <td>-1150</td>\n", "      <td>670.972</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>648 rows × 3 columns</p>\n", "</div>"], "text/plain": ["        X     Y    Value\n", "0   -1300  1150  663.888\n", "1   -1200  1150  671.009\n", "2   -1100  1150  671.009\n", "3   -1000  1150  653.751\n", "4    -900  1150  653.751\n", "..    ...   ...      ...\n", "643   900 -1150  668.462\n", "644  1000 -1150  668.462\n", "645  1100 -1150  663.832\n", "646  1200 -1150  663.832\n", "647  1300 -1150  670.972\n", "\n", "[648 rows x 3 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["thk_df"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["for j, x in enumerate(gas_df.columns):\n", "    for i, y in enumerate(gas_df.index):\n", "        x, y = int(x), int(y)\n", "        value = thk_df[(thk_df['X'] == x) & (thk_df['Y'] == y)]['Value'].values[0]\n", "        thk_matrix[i, j] = value"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["thk_matrix_df = pd.DataFrame(thk_matrix, index=gas_df.index, columns=gas_df.columns)\n", "thk_matrix_df.to_excel(\"thk.xlsx\")"]}], "metadata": {"kernelspec": {"display_name": "deepl", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}