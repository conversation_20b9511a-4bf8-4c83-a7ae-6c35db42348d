import torch
import torch.nn as nn
import torch.nn.functional as F

# 定义CAE（卷积自编码器）类，继承自nn.Module。默认通道数从1逐渐增加到128。
class CAE(nn.Module):
    def __init__(self, channels=[1, 16, 32, 64, 128]):
        # 调用父类初始化方法，保存通道配置
        super(CAE, self).__init__()
        self.channels = channels
        # 构建编码器和解码器网络
        self.encoder = build_encoder(channels)
        self.decoder = build_decoder(channels)
        # 添加编码（encode）输出层，将最后的通道数（128）转换为1通道，用于厚度预测
        self.encode_output_layer = nn.Conv2d(channels[-1], 1, kernel_size=3, stride=1, padding=1)
        # 定义厚度预测的可学习参数：基础值660和缩放因子10
        '''
        nn.Parameter() 包装

        将普通张量转换为可训练参数
        这些参数会：
            自动添加到模型的参数列表中
            参与梯度计算和反向传播
            在训练过程中被优化器更新
        '''
        self.thickness_base = nn.Parameter(torch.tensor(660.0))
        self.thickness_scale = nn.Parameter(torch.tensor(10.0))

    # 厚度反归一化函数：将预测值按比例缩放并加上基础值
    def denormalize_thickness(self, x):
        return x * self.thickness_scale + self.thickness_base

    # 前向传播：输入经过编码器，然后解码重建；同时从编码特征预测厚度。返回重建图像和厚度预测
    def forward(self, x):
        encoded = self.encoder(x)
        decoded = self.decoder(encoded)

        encode_output = self.encode_output_layer(encoded)
        thk_pred = self.denormalize_thickness(encode_output)
        return decoded, thk_pred

# 残差块定义
class ResidualBlock(nn.Module):
    def __init__(self, channels):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(channels)
        self.conv2 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(channels)

    def forward(self, x):
        residual = x
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += residual
        return F.relu(out)

# 注意力块定义
# 注意力块：使用全局平均池化、两个1x1卷积层（降维再升维）和Sigmoid激活生成注意力权重
class AttentionBlock(nn.Module):
    def __init__(self, channels):
        super(AttentionBlock, self).__init__()
        self.attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 8, 1),
            nn.ReLU(),
            nn.Conv2d(channels // 8, channels, 1),
            nn.Sigmoid(),
        )
    # 将注意力权重与输入特征相乘，实现通道注意力机制
    def forward(self, x):
        att = self.attention(x)
        return x * att

# 编码器构建函数
def build_encoder(channels):
    # 创建空列表，用于存储所有网络层
    layers = []
    # 4次特征提取
    for i in range(len(channels) - 1):
        layers.extend(
            [
                # 卷积层，提取特征并增加通道数
                nn.Conv2d(channels[i], channels[i + 1], kernel_size=3, stride=1, padding=1),
                # 批归一化，稳定训练
                nn.BatchNorm2d(channels[i + 1]),
                nn.ReLU(),
                # 残差块，防止梯度消失
                ResidualBlock(channels[i + 1]),
                # 注意力机制，突出重要特征
                AttentionBlock(channels[i + 1]),
            ]
        )
    '''
    *layers 将列表展开为独立参数
    nn.Sequential 将所有层按顺序连接成一个完整网络
    '''
    return nn.Sequential(*layers)

# 解码器构建函数
def build_decoder(channels):
    layers = []
    for i in range(len(channels) - 1, 0, -1):
        layers.extend(
            [
                # 转置卷积层
                nn.ConvTranspose2d(channels[i], channels[i - 1], kernel_size=3, stride=1, padding=1),
                nn.BatchNorm2d(channels[i - 1]),
                nn.ReLU(),
                ResidualBlock(channels[i - 1]),
            ]
        )

    # 输出层 - 针对厚度预测优化
    layers.extend(
        [
            # 这个设计通过通道数递减（channels[0] → 16 → 8 → 1）逐步聚焦到最终的厚度预测任务
            nn.Conv2d(channels[0], 16, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(16),
            nn.ReLU(),
            nn.Conv2d(16, 8, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(8),
            nn.ReLU(),
            nn.Conv2d(8, 1, kernel_size=1),
        ]
    )
    return nn.Sequential(*layers)



