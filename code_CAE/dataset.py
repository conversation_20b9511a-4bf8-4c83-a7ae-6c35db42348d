import torch
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from torch.utils.data import Dataset

# 定义了一个继承自PyTorch Dataset的自定义数据集类
class MyDataset(Dataset):
    # 初始化函数，接收输入矩阵X和输出矩阵Y作为参数，并将其存储为类的属性
    # __init__ 是类的构造函数，在创建类实例时自动调用
    def __init__(self, X, Y):
        self.X = X
        self.Y = Y

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.Y[idx]
    '''
    定义了两个方法：__len__ 和 __getitem__
    __len__ 方法返回数据集的长度，即输入矩阵X的长度
    __getitem__ 方法返回数据集的第idx个样本，即输入矩阵X和输出矩阵Y的第idx个元素

    例如：
        创建数据集实例：
            X = torch.randn(100, 10)  # 100个样本，每个10维
            Y = torch.randn(100, 1)   # 100个对应的标签
            dataset = MyDataset(X, Y)

        获取数据集大小：
            print(len(dataset))  # 输出: 100

        获取单个样本：
            sample_x, sample_y = dataset[0]  # 获取第一个样本

        获取多个样本：
            batch_x, batch_y = dataset[0:5]  # 获取前5个样本

    '''
    

# 定义矩阵分割函数，将大矩阵分割成多个小的patch（块），并返回一个包含所有小矩阵的列表
def split_matrix_to_patches(matrix, patch_size):
    """
    将矩阵分割成n×n的小矩阵(patch)

    参数:
        matrix: 输入矩阵
        patch_size: 小矩阵的尺寸(n)

    返回:
        patches: 包含所有小矩阵的列表
    """
    height, width = matrix.shape
    patches = []

    # 计算可以提取的完整patch的行数和列数
    rows = height - patch_size + 1
    cols = width - patch_size + 1

    # 提取所有可能的patch
    for i in range(rows):
        for j in range(cols):
            patch = matrix[i : i + patch_size, j : j + patch_size]
            patches.append(patch)

    return np.array(patches)

# 定义数据集创建函数，创建训练和测试数据集，参数包括输入矩阵、输出矩阵、patch大小、测试集比例和随机种子。
def create_dataset(X_matrix, Y_matrix, patch_size, test_size=0.2, random_state=42):
    """
    从X和Y矩阵创建训练集和测试集

    参数:
        X_matrix: 输入矩阵X
        Y_matrix: 输出矩阵Y
        patch_size: 小矩阵的尺寸(n)
        test_size: 测试集的比例
        random_state: 随机种子

    返回:
        X_train, X_test, Y_train, Y_test: 训练集和测试集
    """
    # 分别对X和Y矩阵进行patch分割
    X_patches = split_matrix_to_patches(X_matrix, patch_size)
    Y_patches = split_matrix_to_patches(Y_matrix, patch_size)

    # 重塑patch为适合CAE的形状 (样本数, 1, patch_size, patch_size)
    X_patches = X_patches.reshape(-1, 1, patch_size, patch_size)
    Y_patches = Y_patches.reshape(-1, 1, patch_size, patch_size)

    # 使用sklearn的train_test_split函数将数据分割为训练集和测试集
    X_train, X_test, Y_train, Y_test = train_test_split(
        X_patches, Y_patches, test_size=test_size, random_state=random_state
    )

    return X_train, X_test, Y_train, Y_test

# 定义数据获取函数
def get_data(gas_file, thk_file):
    # 读取两个Excel文件，分别包含气体数据和厚度数据，第一列作为索引
    gas_df = pd.read_excel(gas_file, index_col=0)
    thk_df = pd.read_excel(thk_file, index_col=0)
    # 对气体数据进行标准化处理，返回标准化后的矩阵和标准化器。
    gas_matrix, gas_scaler = standardize_data(gas_df.values)
    # 将厚度数据转换为PyTorch张量，并转换为浮点类型
    thk_matrix = torch.from_numpy(thk_df.values).float()
    # 将气体数据转换为PyTorch张量，并转换为浮点类型
    gas_matrix = torch.from_numpy(gas_matrix).float()
    # 返回气体数据矩阵、标准化器和厚度数据矩阵
    return gas_matrix, gas_scaler, thk_matrix

# 定义数据标准化函数
def standardize_data(matrix):
    """
    标准化数据
    """
    # 全局标准化
    # 保存原始矩阵形状
    shape = matrix.shape
    # 创建StandardScaler对象
    scaler = StandardScaler()
    # 对矩阵进行标准化处理
    standardized_matrix = scaler.fit_transform(matrix.reshape(-1, 1)).reshape(shape)
    # 返回标准化后的矩阵和标准化器
    return standardized_matrix, scaler
