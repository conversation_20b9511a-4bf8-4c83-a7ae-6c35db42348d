import torch
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from train import DualObjectiveCAE
from dataset import standardize_data


def split_matrix_to_patches_non_overlapping(matrix, patch_size):
    """
    将矩阵分割成n×n的小矩阵(patch)，尽量不重叠
    如果矩阵尺寸不能被patch_size整除，最后一块允许重叠以确保完整覆盖

    参数:
        matrix: 输入矩阵 (numpy array 或 torch tensor)
        patch_size: 小矩阵的尺寸(n)

    返回:
        patches: 包含所有小矩阵的numpy数组

    示例:
        对于27*24的矩阵，使用4*4的patch_size:
        - 高度27: 划分为 0-3, 4-7, 8-11, 12-15, 16-19, 20-23, 23-26 (最后一个重叠)
        - 宽度24: 划分为 0-3, 4-7, 8-11, 12-15, 16-19, 20-23 (正好整除)
    """
    # 转换为numpy数组以便处理
    if torch.is_tensor(matrix):
        matrix = matrix.numpy()

    height, width = matrix.shape
    patches = []

    # 计算行的起始位置
    row_starts = []
    i = 0
    while i < height:
        if i + patch_size <= height:
            row_starts.append(i)
            i += patch_size
        else:
            # 最后一行，如果剩余部分不足patch_size，就重叠以确保覆盖
            if height - i > 0:  # 确保还有剩余部分需要覆盖
                row_starts.append(height - patch_size)
            break

    # 计算列的起始位置
    col_starts = []
    j = 0
    while j < width:
        if j + patch_size <= width:
            col_starts.append(j)
            j += patch_size
        else:
            # 最后一列，如果剩余部分不足patch_size，就重叠以确保覆盖
            if width - j > 0:  # 确保还有剩余部分需要覆盖
                col_starts.append(width - patch_size)
            break

    # 提取patches
    for row_start in row_starts:
        for col_start in col_starts:
            patch = matrix[row_start : row_start + patch_size, col_start : col_start + patch_size]
            patches.append(patch)

    return np.array(patches)


def reconstruct_matrix_from_patches(patches, original_shape, patch_size):
    """
    从patches重构原始矩阵
    处理重叠区域时使用平均值

    参数:
        patches: patch数组
        original_shape: 原始矩阵的形状 (height, width)
        patch_size: patch的尺寸

    返回:
        reconstructed_matrix: 重构的矩阵
    """
    height, width = original_shape
    reconstructed = np.zeros((height, width))
    count_matrix = np.zeros((height, width))

    # 重新计算patch的位置（与split函数保持一致）
    row_starts = []
    i = 0
    while i < height:
        if i + patch_size <= height:
            row_starts.append(i)
            i += patch_size
        else:
            if height - i > 0:
                row_starts.append(height - patch_size)
            break

    col_starts = []
    j = 0
    while j < width:
        if j + patch_size <= width:
            col_starts.append(j)
            j += patch_size
        else:
            if width - j > 0:
                col_starts.append(width - patch_size)
            break

    # 将patches放回原位置
    patch_idx = 0
    for row_start in row_starts:
        for col_start in col_starts:
            reconstructed[row_start : row_start + patch_size, col_start : col_start + patch_size] += patches[
                patch_idx
            ]
            count_matrix[row_start : row_start + patch_size, col_start : col_start + patch_size] += 1
            patch_idx += 1

    # 对重叠区域取平均值
    reconstructed = np.divide(
        reconstructed, count_matrix, out=np.zeros_like(reconstructed), where=count_matrix != 0
    )

    return reconstructed


def predict_all(patch_size=7):
    """
    加载模型并预测
    """
    # 读取一个 24 * 27 的测试矩阵
    gas_matrix = pd.read_excel("../data/gas.xlsx", index_col=0).values
    gas_matrix_scaled, gas_scaler = standardize_data(gas_matrix)
    print(f"原始矩阵形状: {gas_matrix.shape}")
    print(f"Patch尺寸: {patch_size}x{patch_size}")

    # 分割矩阵并转化为张量
    patches = split_matrix_to_patches_non_overlapping(gas_matrix_scaled, patch_size)
    print(f"分割后的矩阵形状: {patches.shape}")
    patches = torch.from_numpy(patches).float()
    batch_size = patches.shape[0]
    patches = patches.reshape(batch_size, 1, patch_size, patch_size)

    # 加载模型
    trainer = DualObjectiveCAE(patch_size=patch_size, X_scaler=gas_scaler)
    trainer.load_checkpoint(f"../output_CAE/checkpoints/{patch_size}/best_model.pth")
    trainer.model.eval()
    with torch.no_grad():
        patches = patches.to(trainer.device)
        recon_output, thk_pred = trainer.model(patches)

    # 重构功能
    thk_pred = thk_pred.cpu().numpy().reshape(batch_size, patch_size, patch_size)
    recon_output = recon_output.cpu().numpy().reshape(batch_size, patch_size, patch_size)
    reconstructed_thk = reconstruct_matrix_from_patches(thk_pred, gas_matrix.shape, patch_size)
    reconstructed_gas = reconstruct_matrix_from_patches(recon_output, gas_matrix.shape, patch_size)
    print(f"\n重构矩阵形状: {reconstructed_thk.shape}, {reconstructed_gas.shape}")
    # print(f"重构误差 (MSE): {np.mean((test_matrix - reconstructed)**2):.10f}")

    # 反标准化
    reconstructed_gas = gas_scaler.inverse_transform(reconstructed_gas)
    # return reconstructed_thk, reconstructed_gas
    thk_matrix = pd.read_excel("../data/thk.xlsx", index_col=0).values.astype(np.float32)
    return gas_matrix, thk_matrix, reconstructed_thk, reconstructed_gas


def plot_comp(real_map, pred_map, name):
    # 计算MSE误差
    mse = np.mean((real_map - pred_map) ** 2)

    # 创建一行两列的子图
    _, axes = plt.subplots(1, 2, figsize=(12, 5))

    # 左侧：真实厚度
    im1 = axes[0].imshow(real_map, cmap="jet")
    axes[0].set_title(f"Real_{name}")
    axes[0].axis("off")
    plt.colorbar(im1, ax=axes[0], fraction=0.046, pad=0.04)

    # 右侧：预测厚度（带 MSE ）
    im2 = axes[1].imshow(pred_map, cmap="jet")
    axes[1].set_title(f"Pred_{name}(MSE: {mse:.4f})")
    axes[1].axis("off")
    plt.colorbar(im2, ax=axes[1], fraction=0.046, pad=0.04)

    plt.tight_layout()
    plt.savefig(f"../output_CAE/img/{patch_size}/{name}_comparison.png", dpi=300, bbox_inches="tight")
    plt.close()


if __name__ == "__main__":
    patch_size = 7
    real_gas, real_thk, pred_thk, pred_gas = predict_all(patch_size)
    plot_comp(real_gas, pred_gas, "Gas")
    plot_comp(real_thk, pred_thk, "Thk")
