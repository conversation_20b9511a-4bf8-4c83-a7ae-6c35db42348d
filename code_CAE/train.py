import os
import torch
import pickle
import numpy as np
import matplotlib.pyplot as plt
import torch.nn.functional as F
import torch.optim as optim

from model import CAE
from tqdm import tqdm
from torch.utils.data import DataLoader
from dataset import MyDataset, get_data, create_dataset


class DualObjectiveCAE:
    def __init__(self, patch_size, lr=0.001, checkpoint_dir="../output_CAE/checkpoints", X_scaler=None):
        self.patch_size = patch_size
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = CAE().to(self.device)
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.checkpoint_dir = f"{checkpoint_dir}/{patch_size}"
        self.X_scaler = X_scaler
        # 损失函数权重
        self.recon_weight = 1.0  # 重构损失权重
        self.align_weight = 1.0  # 对齐损失权重

        # 跟踪最佳损失
        self.best_loss = float("inf")

        # 确保checkpoint目录存在
        os.makedirs(self.checkpoint_dir, exist_ok=True)

    def reconstruction_loss(self, original, reconstructed):
        """重构损失：MSE"""
        return F.mse_loss(reconstructed, original)

    def alignment_loss(self, encoded_x, encoded_y):
        """对齐损失：编码特征的MSE距离"""
        return F.mse_loss(encoded_x, encoded_y)

    def save_checkpoint(self, epoch, losses, is_best=False):
        """
        保存模型检查点

        Args:
            epoch: 当前epoch
            losses: 损失字典
            is_best: 是否为最佳模型
        """
        # 只保存最佳模型
        if is_best:
            checkpoint = {
                "epoch": epoch,
                "model_state_dict": self.model.state_dict(),
                "optimizer_state_dict": self.optimizer.state_dict(),
                "losses": losses,
                "best_loss": self.best_loss,
                "recon_weight": self.recon_weight,
                "align_weight": self.align_weight,
            }
            best_path = os.path.join(self.checkpoint_dir, "best_model.pth")
            torch.save(checkpoint, best_path)
            print(f"Best model saved: {best_path}")

    def load_checkpoint(self, checkpoint_path):
        """
        加载模型检查点

        Args:
            checkpoint_path: 检查点文件路径

        Returns:
            checkpoint: 检查点字典
        """
        if not os.path.exists(checkpoint_path):
            print(f"Checkpoint not found: {checkpoint_path}")
            return None

        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint["model_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
        self.best_loss = checkpoint.get("best_loss", float("inf"))
        self.recon_weight = checkpoint.get("recon_weight", 1.0)
        self.align_weight = checkpoint.get("align_weight", 1.0)

        print(f"Checkpoint loaded from epoch {checkpoint['epoch']}")
        print(f"Best loss: {self.best_loss:.6f}")

        return checkpoint

    def train_step(self, batch_x, batch_y):
        """单步训练"""
        self.model.train()
        self.optimizer.zero_grad()

        # 前向传播
        recon_x, thk_pred = self.model(batch_x)

        # 计算损失
        loss_recon = self.reconstruction_loss(batch_x, recon_x)
        loss_align = self.alignment_loss(thk_pred, batch_y)

        # 总损失
        total_loss = self.recon_weight * loss_recon + self.align_weight * loss_align

        # 反向传播
        total_loss.backward()
        self.optimizer.step()

        return {
            "train_total_loss": total_loss.item(),
            "train_recon_loss": loss_recon.item(),
            "train_align_loss": loss_align.item(),
        }

    def evaluate(self, test_loader):
        """测试评估"""
        self.model.eval()
        with torch.no_grad():
            test_recon_loss = 0
            test_align_loss = 0
            for batch_x, batch_y in test_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)

                recon_x, thk_pred = self.model(batch_x)

                # 计算测试损失
                test_recon_loss += self.reconstruction_loss(batch_x, recon_x).item()
                test_align_loss += self.alignment_loss(thk_pred, batch_y).item()

            test_avg_reco = test_recon_loss / len(test_loader)
            test_avg_align = test_align_loss / len(test_loader)
            test_total_loss = self.recon_weight * test_avg_reco + self.align_weight * test_avg_align

            return {
                "test_total_loss": test_total_loss,
                "test_recon_loss": test_avg_reco,
                "test_align_loss": test_avg_align,
            }

    def _plot_comparison(self, real_data, pred_data, data_type, output_subdir):
        """
        通用的对比图绘制函数

        Args:
            real_data: 真实数据列表
            pred_data: 预测数据列表
            data_type: 数据类型名称 ('thk' 或 'gas')
            output_subdir: 输出子目录名
        """
        # 计算总的MSE误差
        total_mse = 0
        for real, pred in zip(real_data, pred_data):
            total_mse += np.mean((real - pred) ** 2)
        total_mse /= len(real_data)
        print(f"Total {data_type.upper()} MSE: {total_mse:.6f}")

        # 绘图对比
        output_dir = f"../output_CAE/img/{self.patch_size}/{output_subdir}"
        os.makedirs(output_dir, exist_ok=True)

        # 每4个画一次图
        for i in range(0, len(real_data), 4):
            # 当前批次的数据
            batch_real = real_data[i : i + 4]
            batch_pred = pred_data[i : i + 4]
            batch_size = len(batch_real)

            # 创建子图：2行，batch_size列
            fig, axes = plt.subplots(2, batch_size, figsize=(batch_size * 3, 6))

            # 如果只有一个样本，axes需要reshape
            if batch_size == 1:
                axes = axes.reshape(2, 1)

            for j in range(batch_size):
                # 计算当前样本的MSE误差
                sample_mse = np.mean((batch_real[j] - batch_pred[j]) ** 2)

                # 上面一行：真实值
                im1 = axes[0, j].imshow(batch_real[j], cmap="jet", aspect="auto")
                axes[0, j].set_title(f"Real {data_type.upper()} {i+j+1}")
                axes[0, j].axis("off")
                plt.colorbar(im1, ax=axes[0, j], fraction=0.046, pad=0.04)

                # 下面一行：预测值（标题中包含MSE误差）
                im2 = axes[1, j].imshow(batch_pred[j], cmap="jet", aspect="auto")
                axes[1, j].set_title(f"Pred {data_type.upper()} {i+j+1} MSE: {sample_mse:.4f}")
                axes[1, j].axis("off")
                plt.colorbar(im2, ax=axes[1, j], fraction=0.046, pad=0.04)

            plt.tight_layout()

            # 保存图片
            plot_path = os.path.join(output_dir, f"{data_type}_comparison_batch_{i//4 + 1:03d}.png")
            plt.savefig(plot_path, dpi=200, bbox_inches="tight")
            plt.close()

            print(f"Saved {data_type} comparison plot: {plot_path}")

    def evaluate_plot(self, test_loader_plot, patch_size):
        self.model.eval()
        real_thk = []
        pred_thk = []
        real_gas = []
        pred_gas = []

        with torch.no_grad():
            for batch_x, batch_y in tqdm(test_loader_plot, desc="Evaluating", unit="batch"):
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                gas_pred, thk_pred = self.model(batch_x)

                # 收集 thk 数据
                real_thk.append(batch_y.reshape(patch_size, patch_size).cpu().numpy())
                pred_thk.append(thk_pred.reshape(patch_size, patch_size).cpu().numpy())

                # 收集 gas 数据并还原到真实值
                batch_x_np = batch_x.reshape(patch_size, patch_size).cpu().numpy()
                gas_pred_np = gas_pred.reshape(patch_size, patch_size).cpu().numpy()

                # 使用 X_scaler 还原真实值
                if self.X_scaler is not None:
                    # 将 2D 数据 flatten 为 1D，进行逆变换，再 reshape 回 2D
                    batch_x_flat = batch_x_np.flatten().reshape(-1, 1)
                    gas_pred_flat = gas_pred_np.flatten().reshape(-1, 1)

                    batch_x_restored = self.X_scaler.inverse_transform(batch_x_flat).reshape(
                        patch_size, patch_size
                    )
                    gas_pred_restored = self.X_scaler.inverse_transform(gas_pred_flat).reshape(
                        patch_size, patch_size
                    )

                    real_gas.append(batch_x_restored)
                    pred_gas.append(gas_pred_restored)
                else:
                    # 如果没有 scaler，直接使用原始数据
                    real_gas.append(batch_x_np)
                    pred_gas.append(gas_pred_np)

        # 画 thk 对比图
        self._plot_comparison(real_thk, pred_thk, "thk", "thk")

        # 画 gas 重构对比图
        self._plot_comparison(real_gas, pred_gas, "gas", "gas")

    def train(self, dataloader, test_loader, epochs=100):
        """
        训练循环

        Args:
            dataloader: 训练数据加载器
            test_loader: 测试数据加载器
            epochs: 训练轮数
        """
        train_losses_dict = {"train_total_loss": [], "train_recon_loss": [], "train_align_loss": []}
        test_losses_dict = {"test_total_loss": [], "test_recon_loss": [], "test_align_loss": []}
        for epoch in range(epochs):
            total_losses = {"train_total_loss": 0, "train_recon_loss": 0, "train_align_loss": 0}
            num_batches = 0

            for batch_x, batch_y in tqdm(dataloader, desc=f"Epoch {epoch+1}/{epochs}", unit="batch"):
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)

                losses = self.train_step(batch_x, batch_y)

                for key in total_losses:
                    total_losses[key] += losses[key]

                num_batches += 1

            # 计算平均训练损失
            avg_losses = {k: v / num_batches for k, v in total_losses.items()}
            for key in train_losses_dict:
                train_losses_dict[key].append(avg_losses[key])

            # 进行测试评估
            test_losses = self.evaluate(test_loader)
            current_test_loss = test_losses["test_total_loss"]
            for key in test_losses_dict:
                test_losses_dict[key].append(test_losses[key])

            # 检查是否为最佳模型（基于测试损失）
            is_best = current_test_loss < self.best_loss
            if is_best:
                self.best_loss = current_test_loss
                # 只在找到更好的模型时保存
                self.save_checkpoint(epoch + 1, {**avg_losses, **test_losses}, is_best)

            # 输出平均损失
            if (epoch + 1) % 5 == 0:
                print(f"Epoch {epoch+1}/{epochs}:")
                print(f"  Train Total Loss: {avg_losses['train_total_loss']:.6f}")
                print(f"  Train Recon Loss: {avg_losses['train_recon_loss']:.6f}")
                print(f"  Train Align Loss: {avg_losses['train_align_loss']:.6f}")
                print(f"  Test Total Loss: {test_losses['test_total_loss']:.6f}")
                print(f"  Test Recon Loss: {test_losses['test_recon_loss']:.6f}")
                print(f"  Test Align Loss: {test_losses['test_align_loss']:.6f}")
                if is_best:
                    print(f"  *** New best model! ***")
        return train_losses_dict, test_losses_dict


def loss_output(output_dir, loss_dict):
    for loss_name, loss_list in loss_dict.items():
        with open(f"{output_dir}/{loss_name}.txt", "w") as f:
            for loss in loss_list:
                f.write(f"{loss}\n")


# 设置参数
epochs = 200
patch_size = 7
learning_rate = 1e-3
train_batch_size = 16
test_batch_size = 32

# 数据集的构建
train_dataset_path = f"../data/train_dataset_{patch_size}.pkl"
test_dataset_path = f"../data/test_dataset_{patch_size}.pkl"
X, X_scaler, Y = get_data("../data/gas.xlsx", "../data/thk.xlsx")
if not os.path.exists(train_dataset_path):
    X_train, X_test, Y_train, Y_test = create_dataset(X, Y, patch_size)
    train_dataset = MyDataset(X_train, Y_train)
    test_dataset = MyDataset(X_test, Y_test)
    with open(train_dataset_path, "wb") as f:
        pickle.dump(train_dataset, f)
    with open(test_dataset_path, "wb") as f:
        pickle.dump(test_dataset, f)
else:
    with open(train_dataset_path, "rb") as f:
        train_dataset = pickle.load(f)
    with open(test_dataset_path, "rb") as f:
        test_dataset = pickle.load(f)

train_loader = DataLoader(train_dataset, batch_size=train_batch_size, shuffle=True)
test_loader = DataLoader(test_dataset, batch_size=test_batch_size, shuffle=False)
test_loader_plot = DataLoader(test_dataset, batch_size=1, shuffle=False)

print(f"Length of train_loader: {len(train_loader)}")
print(f"Length of test_loader: {len(test_loader)}")

# 训练示例
if __name__ == "__main__":
    action = "Train"
    # 创建模型
    Trainer = DualObjectiveCAE(lr=learning_rate, patch_size=patch_size, X_scaler=X_scaler)
    if action == "Train":
        train_losses_dict, test_losses_dict = Trainer.train(train_loader, test_loader, epochs=epochs)
        os.makedirs(f"../output_CAE/loss/{patch_size}/", exist_ok=True)
        loss_output(f"../output_CAE/loss/{patch_size}/", train_losses_dict)
        loss_output(f"../output_CAE/loss/{patch_size}/", test_losses_dict)
    # 使用 best_model.pth 在测试集上进行测试
    Trainer.load_checkpoint(f"../output_CAE/checkpoints/{patch_size}/best_model.pth")
    Trainer.evaluate_plot(test_loader_plot, patch_size)
