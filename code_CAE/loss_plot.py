import matplotlib.pyplot as plt
import os

patch_size = 7
loss_directory = "../output_CAE/loss"
loss_filepath = f"{loss_directory}/{patch_size}"
output_dir = f"../output_CAE/img/{patch_size}/loss"

# Create output directory if it doesn't exist
os.makedirs(output_dir, exist_ok=True)

# Read train loss data
train_loss_file = f"{loss_filepath}/train_total_loss.txt"
with open(train_loss_file, "r") as f:
    train_losses = [float(line.strip()) for line in f.readlines()]

# Read test loss data
test_loss_file = f"{loss_filepath}/test_total_loss.txt"
with open(test_loss_file, "r") as f:
    test_losses = [float(line.strip()) for line in f.readlines()]

# Create the plot
plt.figure(figsize=(12, 8))
plt.plot(range(1, len(train_losses) + 1), train_losses, label="Train Loss", linewidth=2, color="blue")
plt.plot(range(1, len(test_losses) + 1), test_losses, label="Test Loss", linewidth=2, color="red")

plt.xlabel("Epoch", fontsize=12)
plt.ylabel("Loss", fontsize=12)
plt.title(f"Training and Test Loss Over Epoch (Patch Size: {patch_size})", fontsize=14)
plt.legend(fontsize=12)
plt.grid(True, alpha=0.3)

# Set y-axis to start from 0 for better visualization
plt.ylim(bottom=0)

# Save the plot
output_file = f"{output_dir}/train_test_loss_comparison.png"
plt.savefig(output_file, dpi=300, bbox_inches="tight")
print(f"Loss comparison plot saved to: {output_file}")

plt.show()
