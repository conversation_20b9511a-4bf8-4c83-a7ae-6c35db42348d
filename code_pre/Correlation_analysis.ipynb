{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import r2_score\n", "import platform"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 设置中文显示\n", "if platform.system().lower() == \"windows\":\n", "    plt.rcParams[\"font.sans-serif\"] = [\"Microsoft YaHei\"]  # Windows\n", "elif platform.system().lower() == \"linux\":\n", "    plt.rcParams[\"font.sans-serif\"] = [\"WenQuanYi Micro Hei\"]  # Linux\n", "else:\n", "    plt.rcParams[\"font.sans-serif\"] = [\"PingFang HK\"]  # macOS\n", "\n", "plt.rcParams[\"axes.unicode_minus\"] = False"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["      -1300  -1200  -1100  -1000  -900   -800   -700   -600   -500   -400   \\\n", "1150   96.8   89.9   94.4   98.7   87.7   89.8   91.5   97.8   89.8   85.2   \n", "1050   95.3   74.5   74.6   77.7   72.2   82.3   88.2   89.8   84.2   95.1   \n", "950    74.0   89.9   92.9   79.5   71.9   90.2   75.2   82.3   76.8   92.8   \n", "850    93.5   89.8   91.9   93.7   98.2   73.4   89.8   87.7   84.8   83.6   \n", "750    76.0   75.5   73.8   73.5   76.9   93.4   76.8   91.8   79.5   95.7   \n", "\n", "      ...   400    500    600    700    800    900    1000   1100   1200  \\\n", "1150  ...   84.3  97.40   94.7   98.3   78.6   78.5   92.7   85.3   81.9   \n", "1050  ...   74.6  78.10   94.7   97.3   92.5   93.4   70.1   85.4   94.9   \n", "950   ...   70.1  77.22   88.5   88.1   71.2   96.1   89.8   66.1   95.2   \n", "850   ...   67.4  96.40   77.9   80.4   77.6   87.5   79.8   77.2   95.7   \n", "750   ...   77.1  78.20   74.5   67.6   72.6   68.2   93.1   89.3   85.8   \n", "\n", "       1300  \n", "1150   72.6  \n", "1050   82.3  \n", "950    69.8  \n", "850   102.8  \n", "750    99.1  \n", "\n", "[5 rows x 27 columns]\n", "      X     Y    Value\n", "0 -1300  1150  663.888\n", "1 -1200  1150  671.009\n", "2 -1100  1150  671.009\n", "3 -1000  1150  653.751\n", "4  -900  1150  653.751\n"]}], "source": ["# 读取xlsx文件\n", "file_path_1 = \"../data/gas.xlsx\"\n", "file_path_2 = \"../output_pre/linear_Interpolated_Data_With_Linear_Extrapolation.csv\"\n", "Gas_df = pd.read_excel(file_path_1, index_col=0)\n", "thk_df = pd.read_csv(file_path_2)\n", "print(Gas_df.head(5))\n", "print(thk_df.head(5))\n", "\n", "# 获取行名和列名\n", "Gas_row_names = Gas_df.index.tolist()\n", "Gas_column_names = Gas_df.columns.tolist()\n", "\n", "# print(\"行名:\", row_names)\n", "# print(\"列名:\", column_names)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["thk_gas = []\n", "\n", "for y in Gas_row_names:\n", "    for x in Gas_column_names:\n", "        thk = thk_df[(thk_df['X'] == int(x)) & (thk_df['Y'] == int(y))]['Value'].values[0]\n", "        gas = Gas_df.loc[y, x]\n", "        thk_gas.append((thk,gas))\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["线性拟合结果: y = -0.119289x + 160.636228\n", "R²: 0.025231\n"]}, {"data": {"image/png": "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**************************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 提取x和y数据\n", "x_data = np.array([item[0] for item in thk_gas]).reshape(-1, 1)  # 注意reshape为二维数组\n", "y_data = np.array([item[1] for item in thk_gas])\n", "\n", "# 创建并训练线性回归模型\n", "model = LinearRegression()\n", "model.fit(x_data, y_data)\n", "\n", "# 获取斜率和截距\n", "slope = model.coef_[0]\n", "intercept = model.intercept_\n", "\n", "# 打印拟合结果\n", "print(f\"线性拟合结果: y = {slope:.6f}x + {intercept:.6f}\")\n", "\n", "# 计算拟合值\n", "y_pred = model.predict(x_data)\n", "\n", "# 计算决定系数 R²\n", "r_squared = r2_score(y_data, y_pred)\n", "print(f\"R²: {r_squared:.6f}\")\n", "\n", "# 可视化结果\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(x_data, y_data, alpha=0.5, label='原始数据')\n", "plt.plot(x_data, y_pred, 'r-', label=f'拟合线: y = {slope:.6f}x + {intercept:.6f}')\n", "plt.xlabel('thk')\n", "plt.ylabel('gas')\n", "plt.title(' gas-thk一元线性拟合')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.savefig('../output_pre/gas-thk一元线性拟合.png', dpi=300)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["互信息: [0.05558612]\n"]}], "source": ["from sklearn.feature_selection import mutual_info_regression\n", "\n", "# 计算互信息\n", "mi = mutual_info_regression(x_data, y_data)\n", "print(\"互信息:\", mi)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["皮尔森相关系数: [-0.15884114]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_18500\\1281390591.py:31: DeprecationWarning: Conversion of an array with ndim > 0 to a scalar is deprecated, and will error in future. Ensure you extract a single element from your array before performing this operation. (Deprecated NumPy 1.25.)\n", "  denominator = math.sqrt(sum_x_diff_squared * sum_y_diff_squared)\n"]}], "source": ["import math\n", "\n", "def calculate_pearson_correlation(x, y):\n", "    # 检查数组长度是否相同\n", "    if len(x) != len(y):\n", "        return \"错误：两个数组长度必须相同\"\n", "    \n", "    n = len(x)\n", "    \n", "    # 计算x和y的平均值\n", "    sum_x = sum(x)\n", "    sum_y = sum(y)\n", "    mean_x = sum_x / n\n", "    mean_y = sum_y / n\n", "    \n", "    # 计算分子：(xi - x̄)(yi - ȳ)的和\n", "    numerator = 0\n", "    # 计算分母部分：(xi - x̄)²的和 和 (yi - ȳ)²的和\n", "    sum_x_diff_squared = 0\n", "    sum_y_diff_squared = 0\n", "    \n", "    for i in range(n):\n", "        x_diff = x[i] - mean_x\n", "        y_diff = y[i] - mean_y\n", "        \n", "        numerator += x_diff * y_diff\n", "        sum_x_diff_squared += x_diff * x_diff\n", "        sum_y_diff_squared += y_diff * y_diff\n", "    \n", "    # 计算分母：√[Σ(xi - x̄)² × Σ(yi - ȳ)²]\n", "    denominator = math.sqrt(sum_x_diff_squared * sum_y_diff_squared)\n", "    \n", "    # 避免除以0\n", "    if denominator == 0:\n", "        return \"无法计算：分母为0，可能是因为其中一个数组中所有值都相同\"\n", "    \n", "    # 计算相关系数\n", "    correlation = numerator / denominator\n", "    \n", "    return correlation\n", "\n", "# 使用示例\n", "result = calculate_pearson_correlation(x_data, y_data)\n", "print(\"皮尔森相关系数:\", result)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[663.888      671.009      671.009      653.751      653.751\n", "  652.132      641.557      641.557      636.937      639.669\n", "  639.669      625.815      625.815      622.681      627.314\n", "  627.314      631.266      631.266      638.923      635.253\n", "  635.253      634.173      630.733      630.733      640.636\n", "  640.636      642.65      ]\n", " [663.888      671.20364815 670.63323457 664.34914815 663.03452469\n", "  660.76104402 656.80363906 652.01922222 652.41685185 652.52262963\n", "  652.7755679  646.61235802 642.16322222 640.49976543 640.92408642\n", "  641.68082716 641.97237037 646.15002469 644.49245679 645.47324691\n", "  648.06330534 649.52226156 647.54926543 647.96995679 650.47001235\n", "  646.49469136 642.65      ]\n", " [673.341      674.70421164 676.42189763 674.33547788 672.97568503\n", "  670.55010559 667.6848882  666.27693559 668.34854053 668.33172571\n", "  667.45577479 663.16935503 660.55119454 659.15888199 658.97431409\n", "  657.29142474 654.61673338 651.77784449 649.90150203 652.56273591\n", "  657.51368323 664.5574472  663.17870144 662.27808995 660.70401587\n", "  656.72869489 651.142     ]\n", " [664.417      672.45789107 676.95135404 672.91001204 674.28506142\n", "  674.32774534 671.25550932 668.32841661 667.05723618 667.77532206\n", "  667.93147021 664.23333019 661.82551031 661.35267081 660.12834959\n", "  657.39938663 653.74344145 651.28502224 650.19777532 653.04900989\n", "  657.98104969 664.73881366 662.33783341 661.59409765 664.51830159\n", "  659.0138188  646.845     ]\n", " [664.417      674.80069232 679.11021084 674.60779296 675.32157074\n", "  675.71509317 674.60229814 672.47016839 669.97076643 670.77130964\n", "  668.57133977 665.16097477 664.63072149 664.15788199 662.14059804\n", "  659.73416302 655.24201978 653.95157503 652.86432812 656.04041339\n", "  659.41316149 665.86545963 663.46447937 663.60549517 666.01249448\n", "  659.33153309 646.845     ]\n", " [663.231      675.63216337 682.56486922 677.64079515 675.59299283\n", "  674.66913043 675.79459627 675.72855349 674.73040534 672.32859474\n", "  669.62589694 667.27836608 666.80116855 667.42434161 665.9467698\n", "  662.95323058 657.25816885 656.41960095 657.66540764 663.98948171\n", "  667.36222981 668.01067702 667.56612185 667.00381033 666.20566218\n", "  659.75263132 650.984     ]\n", " [655.158      672.23540123 682.60883333 680.08269753 680.4293642\n", "  679.60571889 676.49883184 676.60496296 677.36424691 674.08880247\n", "  671.586      670.05402469 669.57682716 673.18403704 671.83635802\n", "  668.77017284 662.66222222 663.4602963  664.44640741 665.12462963\n", "  668.97506717 670.02226938 669.44541975 668.89167901 668.45872222\n", "  662.0022037  654.208     ]\n", " [655.158      671.04228805 682.52435944 681.31987091 682.32063519\n", "  682.92536025 679.15268944 678.57595882 679.72102055 676.44557611\n", "  672.70474902 671.10997746 672.9607182  677.73590062 676.39269074\n", "  673.84202507 667.57350656 666.18586634 668.71279227 668.15168116\n", "  669.25559006 671.02732919 670.97272456 670.41898382 670.15559489\n", "  663.10598332 654.208     ]\n", " [649.815      667.58003428 679.15001047 680.5024796  681.36539936\n", "  681.73095031 680.0052236  679.87245986 680.98375347 678.53377701\n", "  675.37928318 670.44348071 672.95568821 679.74149068 681.72743616\n", "  680.34736209 671.53251277 667.47503129 666.24322705 667.09930481\n", "  668.63291925 671.01863354 671.53892807 671.41333586 670.30963216\n", "  663.00884204 653.215     ]\n", " [649.815      665.58059877 677.68176543 678.78979012 679.65270988\n", "  680.88068407 680.14583782 680.01307407 681.77808025 678.71696296\n", "  675.80987654 671.37919753 673.71079012 672.68333951 674.11\n", "  672.72992593 668.65767284 666.33278395 664.1437284  665.33526543\n", "  667.08215938 669.46481041 669.93064815 668.12414815 667.33659259\n", "  660.03580247 653.215     ]\n", " [633.584      657.81284733 676.29309535 677.03445338 678.20237639\n", "  679.59379503 679.16957143 680.18244122 682.40528073 679.14057703\n", "  675.01671544 671.11177716 667.2414765  663.32419255 664.08468637\n", "  663.8087501  664.75936738 663.44275309 661.34654321 662.62185185\n", "  664.56752795 666.9383354  664.97950437 663.23333356 662.84814838\n", "  648.38696465 630.889     ]\n", " [622.757      653.23226819 674.58465091 676.00215524 676.29034043\n", "  677.08447826 677.50714286 679.13725282 681.40962856 678.05922613\n", "  673.77944836 668.15236056 665.03755809 662.47139752 661.13818687\n", "  657.15333502 653.82769036 655.24181382 654.64654321 657.08005613\n", "  659.32248447 661.15386335 659.41681489 657.99730872 656.25934518\n", "  637.6792279  608.836     ]\n", " [622.757      652.50804459 673.86042731 675.26188295 675.52822863\n", "  675.7526087  676.09658385 677.72165677 680.02203159 677.25736278\n", "  672.41669611 666.10822859 662.13103635 660.9328882  658.90060923\n", "  654.91575738 650.61223695 649.91665854 651.69181903 654.79752703\n", "  657.28677019 659.60250932 657.84134905 656.32374411 654.51369301\n", "  635.93357572 608.836     ]\n", " [629.154      654.91582678 674.1039081  674.76995748 675.25790238\n", "  675.63273292 675.01658385 675.97445181 678.26457526 675.99308243\n", "  671.98038946 665.1982907  661.06841416 658.7086646  658.07965225\n", "  656.09505651 653.32900713 652.01164911 651.9882601  655.59628479\n", "  658.94474534 662.49691925 660.40848528 658.40883728 657.68538049\n", "  641.58323066 620.73      ]\n", " [642.526      660.75916049 673.0532716  672.88035802 673.18877778\n", "  673.50771942 673.63303477 675.46966049 678.53802469 676.2266358\n", "  671.6243642  665.90633951 661.77646296 661.96466667 661.84965432\n", "  662.09248148 660.00233951 658.69655556 658.21582716 658.72662963\n", "  662.30561192 665.58489023 663.96520988 663.84744444 664.11677778\n", "  653.17109259 642.727     ]\n", " [642.526      661.1126876  672.22433287 671.39223411 671.2661909\n", "  671.41275155 673.27550932 675.61020911 678.44980653 674.76706549\n", "  670.16479388 665.01973215 664.35911794 664.88010559 666.15479695\n", "  666.09458684 664.59270163 664.22347389 663.74274549 663.22114056\n", "  665.1410559  668.34975155 668.75649712 668.98196695 670.03134967\n", "  659.10478249 642.727     ]\n", " [647.09       661.28101741 670.64743099 669.84751361 669.24594084\n", "  668.56740994 672.45622981 674.72446722 674.34483759 671.0539487\n", "  666.73001127 665.38212982 664.95212982 665.04345963 665.58432137\n", "  665.06244705 661.6914594  662.76037298 663.8048573  664.96817552\n", "  667.26139752 670.27024845 670.31978533 670.26954459 672.234049\n", "  661.00726505 644.914     ]\n", " [647.09       662.21779012 671.5842037  669.64956173 667.72485802\n", "  667.46758638 671.41170531 671.73818519 670.71166667 667.42077778\n", "  667.13692593 665.97846914 664.27402469 664.0597037  662.89285185\n", "  661.71061728 659.53403704 661.28546914 662.65381481 664.20188889\n", "  666.49511088 669.43277732 670.28904321 670.08789506 671.55296296\n", "  660.32617901 644.914     ]\n", " [653.836      665.74401062 672.71524856 668.84549548 667.43490871\n", "  667.27630435 671.57195652 671.89843639 667.9249796  668.10475983\n", "  668.2657228  666.77251292 664.75801457 662.57036025 660.72036025\n", "  659.32297715 658.79581666 659.43258209 660.42958201 661.93884127\n", "  664.18904969 667.12321118 668.4907103  669.41580488 669.94481723\n", "  662.00526493 650.018     ]\n", " [657.407      666.73295207 671.96456936 668.87717249 668.48072188\n", "  668.70968944 670.68934783 671.0158277  669.17702738 670.68370639\n", "  671.09305199 667.30343616 664.80895468 663.12936646 660.88423319\n", "  658.84188751 658.10327007 658.61895184 658.95505061 659.29598413\n", "  661.54619255 667.20644099 668.72011836 669.65058749 672.52047688\n", "  666.52939663 658.266     ]\n", " [657.407      665.89529369 672.04221601 669.37205245 669.12748585\n", "  670.22490683 672.08267081 672.14442167 670.9285965  672.62790515\n", "  672.71125443 668.01831815 663.58269381 662.58639752 659.80985431\n", "  659.95062664 659.91076121 659.78541768 660.12151645 660.34188682\n", "  663.30188199 669.1056087  670.62388854 672.46421164 675.77244134\n", "  670.91229104 658.266     ]\n", " [656.548      666.37363998 672.83289924 671.10326961 670.86720271\n", "  671.68701863 673.27517391 673.72149965 673.47890706 674.28477348\n", "  673.93734138 670.27499571 665.25281543 660.58267081 661.93794326\n", "  663.85443501 667.18591649 666.15678069 664.61841086 666.75772901\n", "  669.5969441  673.22175776 674.67659788 675.97519036 675.01778295\n", "  671.63844962 667.159     ]\n", " [655.62       658.77101235 665.2302716  665.12466667 665.96705556\n", "  666.41930849 665.36651867 669.03004938 670.88979012 671.43188889\n", "  668.87892593 660.6642963  655.87332099 648.4904321  655.33132099\n", "  661.30334568 668.45495062 667.42581481 669.39312346 668.63574074\n", "  669.50380055 671.79509976 672.81857407 671.41341975 669.44868519\n", "  666.06935185 670.972     ]\n", " [655.62       658.245      658.245      660.407      660.407\n", "  661.55       658.258      658.258      667.882      668.329\n", "  668.329      649.681      649.681      637.045      649.753\n", "  649.753      669.215      669.215      673.889      670.391\n", "  670.391      669.777      668.462      668.462      663.832\n", "  663.832      670.972     ]]\n"]}], "source": ["# 提取第三列数据（列名为 'value'）\n", "values = thk_df['Value']\n", "\n", "# 确保数据长度为 648\n", "if len(values) != 648:\n", "    raise ValueError(f\"数据长度为 {len(values)}，无法重塑为 24x27 矩阵。请检查数据！\")\n", "\n", "# 转换为 24x27 的矩阵\n", "matrix = np.array(values).reshape(24, 27)\n", "\n", "# 打印矩阵\n", "print(matrix)\n", "\n", "# 保存为文件\n", "np.savetxt(\"../data/thk_matrix.csv\", matrix, delimiter=\",\")"]}], "metadata": {"kernelspec": {"display_name": "amec", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}